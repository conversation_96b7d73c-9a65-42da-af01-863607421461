import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext';
import { Tag } from 'primereact/tag';
import { API } from '../../../../constants/api_url';
import APIServices from '../../../../service/APIService';
import { Button } from 'primereact/button';
import SupplierPanel from '../SupplierScreen/SupplierPanel';
import { Dialog } from 'primereact/dialog';
// import useForceUpdate from 'use-force-update'; // Removed to prevent unnecessary re-renders
import { Calendar } from 'primereact/calendar';
import moment from 'moment';
import './MSIStyles.css';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
const SuppliersTable = ({ data, assessorList, globalFilter, editSupplier, deleteSupplier }) => {
    console.log(data)
    const [databk, setDatabk] = useState([])
    const [datas, setDatas] = useState([])
    const [attdialog, setAttDialog] = useState(false);
       const [docdialog, setDocDialog] = useState(false)
       const [attachment, setAttachment] = useState([]);
    const [search, setSearch] = useState('')
    const [dateFilter, setDateFilter] = useState({ start: null, end: null })
    // const forceUpdate = useForceUpdate(); // Removed to prevent unnecessary re-renders
    const [reportdialog, setReportDialog] = useState(false)
    const [selectedAttachments, setSelectedAttachments] = useState([])
    const [selectedAudit, setSelectedAudit] = useState(null)
    const [actionModal, setActionModal] = useState(false)
     const [actionModal2, setActionModal2] = useState(false)
      const [actionReportData, setActionReportData] = useState([])
         const [actionStatusReport, setActionStatusReport] = useState(false)
         const [addActionDialogVisible, setAddActionDialogVisible] = useState(false)
         const [selectedSupplier, setSelectedSupplier] = useState(null)
    const [filteredDataCount, setFilteredDataCount] = useState(0);
    const [currentFilteredData, setCurrentFilteredData] = useState([]);
    const dataTableRef = useRef(null);

    // Add state for pagination
    const [rows, setRows] = useState(10);
    const [first, setFirst] = useState(0);
           const [editDialogVisible, setEditDialogVisible] = useState(false);
             const [selectedSupplierForEdit, setSelectedSupplierForEdit] = useState(null);
             const [selectedReviewers, setSelectedReviewers] = useState([]);

    // Add state to preserve DataTable filters
    const [tableFilters, setTableFilters] = useState({
        supplierName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null },
        stat: { matchMode: 'in', value: null },
        calibrationTeamMembers: { matchMode: 'in', value: null }
    });


// Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.supplierName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }
    // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                const dateStr = rowData?.supplierAssignmentSubmission?.submitted_on || rowData?.supplierAssignmentSubmission?.modified_on;
                if (!dateStr) return false;
                 const itemDate = DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();
              return itemDate >= startDate && itemDate <= endDate;
            });
        }
// Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));
         setDatas(indexedData);
    };

    // Memoize the enhanced data to prevent unnecessary re-computations
    const enhancedData = useMemo(() => {
        return datas.map((x, index) => ({
            ...x,
            tableIndex: index + 1
        }));
    }, [datas]);

    // Manual filter calculation function as a fallback
    const calculateFilteredCount = useCallback(() => {
        if (!databk || databk.length === 0) return 0;

        // Start with the original data and apply all filters in the same order as applyFilters
        let filteredDataLocal = [...databk];

        // Apply search filter (same logic as in applyFilters)
        if (search.trim()) {
            filteredDataLocal = filteredDataLocal.filter(x =>
                x?.vendor?.supplierName?.trim().toLowerCase().includes(search?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(search?.trim().toLowerCase())
            );
        }

        // Apply date range filter (same logic as in applyFilters)
        if (dateFilter.start && dateFilter.end) {
            filteredDataLocal = filteredDataLocal.filter(rowData => {
                const dateStr = rowData?.supplierAssignmentSubmission?.submitted_on || rowData?.supplierAssignmentSubmission?.modified_on;
                if (!dateStr) return false;
                const itemDate = DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();
                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        // Apply DataTable column filters
        Object.entries(tableFilters).forEach(([field, filter]) => {
            if (filter.value && filter.value.length > 0) {
                filteredDataLocal = filteredDataLocal.filter(item => {
                    // Special handling for calibrationTeamMembers filter
                    if (field === 'calibrationTeamMembers') {
                        // First, populate the calibrationTeamMembers field if not already done
                        if (!item.calibrationTeamMembers) {
                            const allMembers = [];
                            [item?.group1, item?.group2, item?.group3, item?.group4].forEach(group => {
                                if (group?.assessors?.length) {
                                    const groupMembers = group.assessors
                                        .map(x => assessorList.find(i => i.id === x))
                                        .filter(i => i)
                                        .map(i => i?.information?.empname || '')
                                        .filter(name => name !== '');
                                    allMembers.push(...groupMembers);
                                }
                            });
                            item.calibrationTeamMembers = allMembers;
                        }

                        const teamMembers = item.calibrationTeamMembers || [];

                        // If there are no team members, don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.value.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    } else {
                        // Standard filtering for other fields
                        const fieldValue = item[field];
                        if (fieldValue == null) return false;
                        return filter.value.includes(fieldValue);
                    }
                });
            }
        });

        return filteredDataLocal.length;
    }, [databk, search, dateFilter, tableFilters]);

    // Initialize filtered count when enhanced data changes
    useEffect(() => {
        const calculatedCount = calculateFilteredCount();
        setFilteredDataCount(calculatedCount);
        console.log('Enhanced data changed, updating count to:', calculatedCount);
    }, [enhancedData.length, calculateFilteredCount]);

    // Modified useEffect to only run when data actually changes, not on every render
    useEffect(() => {
        if (JSON.stringify(databk) !== JSON.stringify(data)) {
            setDatabk(data);
            applyFilters(data);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    // Separate useEffect for date filter changes
    useEffect(() => {
        if (databk.length > 0) {
            applyFilters(databk);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);


    const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }, { name: 'IDM (Indirect Material)', value: 10 }, { name: 'Import', value: 11 }]


 const calibrationIdBodyTemplate = (rowData) => {
        return (
            <div >
                {'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
            </div>
        );
    };
    const scoreTemplate = (rowData) => {
        let finished = rowData?.supplierAssignmentSubmission?.type === 1

        return (
            <div className={finished ? 'clr-navy fw-6 fs-14 cur-pointer text-underline' : 'fw-5 fs-14'} onClick={(e) => { e.preventDefault(); if (finished) { setSelectedAudit(rowData); setActionModal(true) } }}>
                {rowData?.supplierAssignmentSubmission?.supplierMSIScore ? rowData?.supplierAssignmentSubmission?.supplierMSIScore : 'NA'}
            </div>
        );

    }
       const getCalibirationId = (rowData) => {
        return 'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')
    }
    const nameTemplate = (rowData) => {
        return (
      rowData?.vendor?.supplierName || 'NA'

        );
    };
    const locationTemplate = (rowData) => {
        return (
            rowData?.vendor?.supplierLocation || 'NA'
          );
    };
       const categoryTemplate = (rowData) => {
        console.log(rowData)
        return (
       categoryList.find(i => i.value === rowData?.vendor?.supplierCategory)?.name || 'Not Found'
         );
    };
    const assessmentDueDate = (rowData) => {
        return (
           DateTime.fromISO(rowData.assessmentEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
        );
    };
     const openActionStatusReport = React.useCallback((rowData) => {
            setActionReportData(rowData);
            setActionStatusReport(true);
        }, []);
    const supplierAssessmentSubmissionDateTemplate = (rowData) => {
        console.log(rowData)
        if (!rowData?.supplierAssignmentSubmission) {
            return 'NA'
        }
        return (
        DateTime.fromISO(rowData?.supplierAssignmentSubmission?.submitted_on || rowData?.supplierAssignmentSubmission?.modified_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
       );
    };
    const calibrationStartDate = (rowData) => {
        console.log(rowData)
        return (
        rowData.auditStartDate ? DateTime.fromISO(rowData.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set'
  );
    };
     const dealerActionReport = (rowData) => {
        openActionStatusReport(rowData);
    }

    const statusTemplate = (rowData) => {
        if (!rowData?.supplierAssignmentSubmission) {
            return <Tag value='Not Started' className='status-tag-orange' />
        } else if (rowData?.supplierAssignmentSubmission?.type !== 1) {
            return <Tag value='InProgress' className='status-tag-blue' />
        } else if (rowData?.supplierAssignmentSubmission?.type === 1) {
            return <Tag value='Completed' className='status-tag-green' />
        }
 }
    const calibrationEndDate = (rowData) => {
        return (
      rowData.auditEndDate ? DateTime.fromISO(rowData.auditEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set'
     );
    };
    const contactTemplate = (rowData) => {
        return (
      rowData?.vendor?.supplierContact || 'NA'
);
    };
    const pySpendBodyTemplate = (rowData) => {
        return `${rowData?.vendor?.supplierSpentOn || '-'} Cr. INR`;
    };
      const openAddActionDialog = React.useCallback((supplier) => {
            setSelectedSupplier(supplier);
            setAddActionDialogVisible(true);
        }, []);
         const addActionTemplate = (rowData) => {
        return (
            <div className="flex justify-content-center">
                <button
                    className="p-button p-button-rounded p-button-text"
                    onClick={() => openAddActionDialog(rowData)}
                >
                    <i className="pi pi-plus-circle" style={{ color: '#315975' }}></i>
                </button>
            </div>
        );
    };

    const RowFilterTemplate = (options, obj) => {
        // Extract unique values for the dropdown
        const uniqueValues = [...new Set(data.map((i) => {
            // Handle nested properties
            if (obj.includes('.')) {
                const parts = obj.split('.');
                let value = i;
                for (const part of parts) {
                    value = value?.[part];
                }
                return value;
            }
            return i[obj];
        }))].filter(x => x);
        // Format the options for the MultiSelect
        let selectOptions;
// Special handling for category which are numeric IDs
        if (obj === 'cat') {
            selectOptions = uniqueValues.map(val => ({
                label: categoryList.find(x => x.value === val)?.name || val?.toString() || '',
                value: val
            }));
        } else {
            selectOptions = uniqueValues.map(val => ({
                label: val?.toString() || '',
                value: val
            }));
        }
         return (
            <MultiSelect
                value={options.value}
                options={selectOptions}
                onChange={(e) => options.filterCallback(e.value)}
                optionLabel="label"
                placeholder="Select"
                filter

                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };



    // Create memoized dialog open functions to prevent unnecessary re-renders
    const openAttDialog = React.useCallback((attachments) => {
        setAttachment(attachments);
        setAttDialog(true);
    }, []);

    const openDocDialog = React.useCallback((attachments) => {
        setSelectedAttachments(attachments);
        setDocDialog(true);
    }, []);

    const openReportDialog = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setReportDialog(true);
    }, []);

    const openActionModal = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setActionModal(true);
    }, []);

    const openActionModal2 = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setActionModal2(true);
    }, []);
    // Merged template for all calibration team members
    const calibrationTeamMembersTemplate = (rowData) => {
        const allMembers = [];

        // Collect members from all groups
        [rowData?.group1, rowData?.group2, rowData?.group3, rowData?.group4].forEach(group => {
            if (group?.assessors?.length) {
                const groupMembers = group.assessors
                    .map(x => assessorList.find(i => i.id === x))
                    .filter(i => i)
                    .map(i => i?.information?.empname || '')
                    .filter(name => name !== '');
                allMembers.push(...groupMembers);
            }
        });

        // Store the team members in rowData for filtering
        rowData.calibrationTeamMembers = allMembers;

        if (allMembers.length === 0) {
            return 'Not Assigned';
        }

        // Return as comma-separated string for display
        return allMembers.join(', ');
    }
  const calibirationtemplate = (rowData) => {
        return (
            <div>
                <Button
                    icon="pi pi-pencil"
                    onClick={() => editSupplier(rowData)}  />
                 </div>
        )
    }
    const deletetemplate = (rowData) => {
        return (
            <div>
                {(!rowData.supplierAssignmentSubmission && !rowData.auditorAssignmentSubmission) ?
                    <Button
                        icon="pi pi-trash"
                        className='mandatory'
                        onClick={() => deleteSupplier(rowData)}
                    /> :
                    'NA'
                }
            </div>
        )
    }
    const searchFn = (e) => {
        let val = e.target.value
        setSearch(val)
        applyFilters(databk, val)
    }
 const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };
    const sortAuditStartDate = (e) => {
        console.log(e.data);
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditStartDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditStartDate, { zone: 'utc' });
                 // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditStartDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditStartDate, { zone: 'utc' });
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
    const sortAuditEndDate = (e) => {
        console.log(e.data);
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditEndDate, { zone: 'utc' });
       // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditEndDate, { zone: 'utc' });
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
// Sort function for PY Spend
    const sortPySpend = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                // Extract numeric value from spend, removing 'Cr. INR' and handling missing values
                const spendA = parseFloat(a?.vendor?.supplierSpentOn || 0);
                const spendB = parseFloat(b?.vendor?.supplierSpentOn || 0);
           return spendA - spendB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const spendA = parseFloat(a?.vendor?.supplierSpentOn || 0);
                const spendB = parseFloat(b?.vendor?.supplierSpentOn || 0);
                return spendB - spendA;
            });
        }
    };
 const exportExcel = () => {
        if (!datas || datas.length === 0) {
            alert('No data to export.');
            return;
        }

     const exportData = datas.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendorCode ? `MSI-${item.vendorCode}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Supplier Name': item.vendor?.supplierName || 'NA',
            'Supplier Location': item.vendor?.supplierLocation || 'NA',
            'Supplier Contact': item.vendor?.supplierContact || 'NA',
            'Category': categoryList.find(x => x.value === item.vendor?.supplierCategory)?.name || 'NA',
            'PY Spend (Cr INR)': item.vendor?.supplierSpentOn ? `${item.vendor.supplierSpentOn} Cr.` : 'NA',
            'Self-Assessment Due Date': item.assessmentEndDate ? DateTime.fromISO(item.assessmentEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'NA',
            'Self-Assessment Status': item.supplierAssignmentSubmission
                ? (item.supplierAssignmentSubmission.type === 1
                    ? 'Completed'
                    : 'In Progress')
                : 'Not Started',
            'MSI Self-Assessment Score': item?.supplierAssignmentSubmission?.supplierMSIScore || 'NA',
            'Self-Assessment Completed Date': item?.supplierAssignmentSubmission?.submitted_on
                ? DateTime.fromISO(item?.supplierAssignmentSubmission?.submitted_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
                : (item?.supplierAssignmentSubmission?.modified_on
                    ? DateTime.fromISO(item?.supplierAssignmentSubmission?.modified_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
                    : 'NA'),
            'Calibration Start Date': item.auditStartDate ? DateTime.fromISO(item.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set',
            'Calibration End Date': item.auditEndDate ? DateTime.fromISO(item.auditEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set',
            'Calibration Team Members': (() => {
                const allMembers = [];
                [item?.group1, item?.group2, item?.group3, item?.group4].forEach(group => {
                    if (group?.assessors?.length) {
                        const groupMembers = group.assessors
                            .map(id => assessorList.find(i => i.id === id)?.information?.empname)
                            .filter(Boolean);
                        allMembers.push(...groupMembers);
                    }
                });
                return allMembers.length > 0 ? allMembers.join(', ') : 'Not Assigned';
            })()
        }));
   const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Suppliers');
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Suppliers_List_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };
    // Sort function for Self-Assessment Due Date
    const sortSelfAssessmentDueDate = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.assessmentEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.assessmentEndDate, { zone: 'utc' });
               // Handle missing dates
                if (!a.assessmentEndDate && !b.assessmentEndDate) return 0;
                if (!a.assessmentEndDate) return 1; // null dates at the end for ascending
                if (!b.assessmentEndDate) return -1;
                  // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.assessmentEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.assessmentEndDate, { zone: 'utc' });
        // Handle missing dates
                if (!a.assessmentEndDate && !b.assessmentEndDate) return 0;
                if (!a.assessmentEndDate) return 1; // null dates at the end for descending too
                if (!b.assessmentEndDate) return -1;
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };

    // Sort function for Self-Assessment Submission Date
    const sortSelfAssessmentSubmissionDate = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                // Get the submission date from either submitted_on or modified_on
                const dateStrA = a?.supplierAssignmentSubmission?.submitted_on || a?.supplierAssignmentSubmission?.modified_on;
                const dateStrB = b?.supplierAssignmentSubmission?.submitted_on || b?.supplierAssignmentSubmission?.modified_on;

                // Handle missing dates
                if (!dateStrA && !dateStrB) return 0;
                if (!dateStrA) return 1; // null dates at the end for ascending
                if (!dateStrB) return -1;

                // Parse the dates
                let dateA = DateTime.fromISO(dateStrA, { zone: 'utc' });
                let dateB = DateTime.fromISO(dateStrB, { zone: 'utc' });

                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                // Get the submission date from either submitted_on or modified_on
                const dateStrA = a?.supplierAssignmentSubmission?.submitted_on || a?.supplierAssignmentSubmission?.modified_on;
                const dateStrB = b?.supplierAssignmentSubmission?.submitted_on || b?.supplierAssignmentSubmission?.modified_on;

                // Handle missing dates
                if (!dateStrA && !dateStrB) return 0;
                if (!dateStrA) return 1; // null dates at the end for descending too
                if (!dateStrB) return -1;

                // Parse the dates
                let dateA = DateTime.fromISO(dateStrA, { zone: 'utc' });
                let dateB = DateTime.fromISO(dateStrB, { zone: 'utc' });

                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
    return (
        <div>
            <div className="col-12 flex justify-content-end mb-3" >
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText value={search} style={{ width: '100%' }} onChange={searchFn} placeholder="Search Code/Name" />
                    </span>
                </div>
            </div>
            <div className="col-12 flex mb-3" >
                <div className="flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Self-Assessment Completion Date From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>Suppliers List ({filteredDataCount})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>
            <DataTable
                ref={dataTableRef}
                value={enhancedData}
                paginator
                rows={rows}
                first={first}
                scrollable
                rowsPerPageOptions={[10, 25, 50, 100]}
                scrollHeight="500px"
                filters={tableFilters}
                filterDisplay="menu"
                onPage={(e) => {
                    console.log('📄 Page changed:', e);
                    setFirst(e.first);
                    setRows(e.rows);
                    // Ensure count remains accurate when navigating pages
                    const currentCount = calculateFilteredCount();
                    if (currentCount !== filteredDataCount) {
                        console.log('🔄 Page change: Updating count to:', currentCount);
                        setFilteredDataCount(currentCount);
                    }
                }}
                onFilter={(e) => {
                    console.log('🔍 DataTable onFilter triggered for Suppliers List');
                    console.log('Event object:', e);

                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    setTableFilters(cleanedFilters);

                    // Update filtered data count based on the actual filtered results
                    // e.filteredValue contains ALL filtered records across all pages
                    let filteredCount;

                    if (e.filteredValue && Array.isArray(e.filteredValue)) {
                        filteredCount = e.filteredValue.length;
                        console.log('✅ Using e.filteredValue.length:', filteredCount);
                    } else {
                        // Fallback to manual calculation
                        filteredCount = calculateFilteredCount();
                        console.log('⚠️ Fallback to manual calculation:', filteredCount);
                    }

                    console.log('🔢 DataTable filters applied:', cleanedFilters);
                    console.log('🔢 Current search filter:', search);
                    console.log('🔢 Current date filter:', dateFilter);
                    console.log('🔢 Final filtered count (total across all pages):', filteredCount);
                    console.log('🔢 Enhanced data length:', enhancedData.length);

                    // Update the count and filtered data immediately
                    setFilteredDataCount(filteredCount);
                    setCurrentFilteredData(e.filteredValue || enhancedData);
                }}
                globalFilter={globalFilter}
                className="mt-2 h-500">
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={(e) => {
                    const { data, order } = e;
                    const indexedData = data.map((item, index) => ({ ...item, tableIndex: index + 1 }));
                    return order === 1 ?
                        indexedData.sort((a, b) => a.tableIndex - b.tableIndex) :
                        indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
                }} />
                <Column field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "msiId")
                    }
                ></Column>
                <Column field="supplierName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "supplierName")
                    }  ></Column>
                <Column field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "location")
                    }  ></Column>
                <Column field="supplierContact" header="Supplier Contact" body={contactTemplate}></Column>
         <Column field="pySpend" sortable sortFunction={sortPySpend} header="PY Spend (Cr. INR)" body={pySpendBodyTemplate} ></Column>
           <Column field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "cat")
                    } ></Column>
                <Column field="selfAssessmentDueDate" sortable sortFunction={sortSelfAssessmentDueDate} header="Self-Assessment Due Date" body={assessmentDueDate} ></Column>
                 <Column field="stat" header="Self-Assessment Status" headerStyle={{ width: 300 }} body={statusTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "stat")
                    }  ></Column>
                <Column header="MSI Self-Assessment Score" sortable field='supplierAssignmentSubmission.supplierMSIScore' body={scoreTemplate}  ></Column>
            <Column header="Self-Assessment Submission Date" sortable field='supplierAssignmentSubmission' body={supplierAssessmentSubmissionDateTemplate} sortFunction={sortSelfAssessmentSubmissionDate}  ></Column>
             <Column field="auditStartDate" sortable sortFunction={sortAuditStartDate} header="Calibration Start Date" body={calibrationStartDate}></Column>
                <Column field="auditEndDate" sortable sortFunction={sortAuditEndDate} header="Calibration End Date" body={calibrationEndDate}></Column>
             <Column
                    field="calibrationTeamMembers"
                    header="Calibration Team Members"
                    body={calibrationTeamMembersTemplate}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => {
                        // Create options from calibrationTeamMembers in all rows
                        const teamOptions = enhancedData
                            .filter(item => item.calibrationTeamMembers && item.calibrationTeamMembers.length > 0)
                            .flatMap(item => item.calibrationTeamMembers);

                        // Remove duplicates
                        const uniqueOptions = [...new Set(teamOptions)].map(name => ({
                            label: name,
                            value: name
                        }));

                        return (
                            <MultiSelect
                                value={options.value}
                                options={uniqueOptions}
                                optionLabel="label"
                                onChange={(e) => options.filterCallback(e.value)}
                                placeholder="Select Member"
                                filter
                                panelClassName='hidefilter'
                                className="p-column-filter"
                                maxSelectedLabels={1}
                                style={{ minWidth: "14rem" }}
                            />
                        );
                    }}
                    filterFunction={(value, filter) => {
                        if (!filter || filter.length === 0) return true;

                        // Use the calibrationTeamMembers array we stored in the rowData
                        const teamMembers = value.calibrationTeamMembers || [];

                        // If there are no team members and the row shows "Not Assigned", don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    }}
                ></Column>
                <Column header="Schedule MSI Calibration" headerStyle={{ width: 150 }} body={calibirationtemplate}></Column>
                <Column header="Action" headerStyle={{ width: 150 }} body={deletetemplate}></Column>
      </DataTable>
            {selectedAudit && <Dialog
                visible={actionModal}
                header={`TVS Motors Supplier MSI Self-assessment ( ${getCalibirationId(selectedAudit)} )`}
                style={{ width: '75%' }}
                onHide={() => setActionModal(false)}
 >
<SupplierPanel
    vendorCode={selectedAudit.vendorCode}
    closeModal={(e) => {
        setActionModal(() => e);
        // Remove forceUpdate() to prevent unnecessary re-renders that reset filters
        // forceUpdate();
    }}
    readOnly={true}
    auditId={selectedAudit}
/>
 </Dialog>
            }
        </div>
    );
};
export default SuppliersTable;
